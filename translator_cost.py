"""
AI Cost Calculator

This module provides functions to calculate the cost of using various AI models
for text generation and translation tasks.
"""
import anthropic
import t<PERSON><PERSON><PERSON>

def calculate_gpt4_1mini_cost(usage):
    """
    Calculate the API cost for a GPT-4.1-mini call based on token usage.
    
    Args:
        usage (dict): A dictionary with keys:
            - 'prompt_tokens': int (number of prompt tokens used)
            - 'completion_tokens': int (number of completion/output tokens)
            - 'prompt_tokens_details': dict (details about cached tokens)
    
    Returns:
        float: The total cost in USD.
    """
    input_cost_per_million = 0.40
    cached_input_cost_per_million = 0.10
    output_cost_per_million = 1.60

    # Extract usage counts
    prompt_tokens = usage.prompt_tokens
    cached_prompt_tokens = usage.prompt_tokens_details.cached_tokens if usage.prompt_tokens_details else 0
    completion_tokens = usage.completion_tokens

    # Calculate real input tokens (non-cached prompt tokens)
    real_prompt_tokens = prompt_tokens - cached_prompt_tokens

    # Calculate costs
    input_cost = (real_prompt_tokens / 1_000_000) * input_cost_per_million
    cached_input_cost = (cached_prompt_tokens / 1_000_000) * cached_input_cost_per_million
    output_cost = (completion_tokens / 1_000_000) * output_cost_per_million

    total_cost = input_cost + cached_input_cost + output_cost
    return total_cost



def calculate_claude_3_7_sonnet_cost(usage):
    """
    Calculate the API cost for claude-3-7-sonnet-20250219 based on token usage.

    Args:
        usage (object): An object or namedtuple with attributes:
            - input_tokens (int)
            - output_tokens (int)

    Returns:
        float: Total cost in USD, rounded to 6 decimal places.
    """
    input_cost_per_million = 3.00
    output_cost_per_million = 15.00

    input_tokens = usage.input_tokens
    output_tokens = usage.output_tokens

    input_cost = (input_tokens / 1_000_000) * input_cost_per_million
    output_cost = (output_tokens / 1_000_000) * output_cost_per_million

    total_cost = input_cost + output_cost
    return round(total_cost, 6)





# def count_tokens_anthropic(text, client):
#     """Count tokens using Anthropic's official method"""
#     return client.count_tokens(text)

# def estimate_tokens_tiktoken(text):
#     """Estimate token count using tiktoken"""
#     encoding = tiktoken.get_encoding("cl100k_base")
#     return len(encoding.encode(text))

# def approximate_tokens(text):
#     """Quick approximation: ~4 characters per token"""
#     return len(text) // 4

# def calculate_claude3_sonnet_cost(usage_or_strings, client=None):
#     """
#     Calculate the API cost for a Claude 3 Sonnet call.
    
#     Args:
#         usage_or_strings: Can be either:
#             - dict with 'input_tokens' and 'output_tokens' keys
#             - dict with 'input_text' and 'output_text' string keys
#             - tuple of (input_string, output_string)
#         client: Anthropic client for accurate token counting (optional)
    
#     Returns:
#         dict: Contains 'total_cost', 'input_cost', 'output_cost', 'input_tokens', 'output_tokens'
#     """
#     # Claude 3 Sonnet pricing
#     input_cost_per_million = 3.00
#     output_cost_per_million = 15.00
    
#     # Handle different input types
#     if isinstance(usage_or_strings, dict):
#         if 'input_tokens' in usage_or_strings:
#             # Already have token counts
#             input_tokens = usage_or_strings['input_tokens']
#             output_tokens = usage_or_strings['output_tokens']
#         else:
#             # Have text strings, need to count tokens
#             input_text = usage_or_strings['input_text']
#             output_text = usage_or_strings['output_text']
            
#             if client:
#                 input_tokens = count_tokens_anthropic(input_text, client)
#                 output_tokens = count_tokens_anthropic(output_text, client)
#             else:
#                 # Use tiktoken as fallback
#                 try:
#                     input_tokens = estimate_tokens_tiktoken(input_text)
#                     output_tokens = estimate_tokens_tiktoken(output_text)
#                 except ImportError:
#                     # Fallback to approximation
#                     input_tokens = approximate_tokens(input_text)
#                     output_tokens = approximate_tokens(output_text)
    
#     elif isinstance(usage_or_strings, tuple):
#         # Tuple of (input_string, output_string)
#         input_text, output_text = usage_or_strings
        
#         if client:
#             input_tokens = count_tokens_anthropic(input_text, client)
#             output_tokens = count_tokens_anthropic(output_text, client)
#         else:
#             try:
#                 input_tokens = estimate_tokens_tiktoken(input_text)
#                 output_tokens = estimate_tokens_tiktoken(output_text)
#             except ImportError:
#                 input_tokens = approximate_tokens(input_text)
#                 output_tokens = approximate_tokens(output_text)
    
#     # Calculate costs
#     input_cost = (input_tokens / 1_000_000) * input_cost_per_million
#     output_cost = (output_tokens / 1_000_000) * output_cost_per_million
#     total_cost = input_cost + output_cost
    
#     return {
#         'total_cost': total_cost,
#         'input_cost': input_cost,
#         'output_cost': output_cost,
#         'input_tokens': input_tokens,
#         'output_tokens': output_tokens
#     }






