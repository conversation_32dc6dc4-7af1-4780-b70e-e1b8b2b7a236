import sys
sys.path.insert(0, '/home/<USER>/bi_python_tool')
import Database_Connection_Class as db
conn = db.DBConnection(mars_cred={'bi_admin': 'work011DAY'},
                       venus_cred={'bi_admin': 'work011DAY'},
                       rs_cred={'bi_admin': 'work011DAY'},
                       rs_sslmode='verify-ca')
from openai import OpenAI
import pandas as pd
import json
from datetime import datetime
import anthropic
import pickle
import re

', '.join([f'bullet{i}' for i in range(1, 17)])

# df = conn.getDfFromRedshift('', f"""select code, name, color, bullet1, bullet2, bullet3, bullet4, bullet5, bullet6, bullet7, bullet8, bullet9, bullet10, bullet11, bullet12, bullet13, bullet14, bullet15, bullet16, ingredients, subbrand, commonDescription, rawDescription from mars__revolveclothing_com___db.product where name != '' limit 30""")
df = pd.read_csv('df.csv')
code = df['code']
df_copy = df.copy()
# df_copy.to_csv('df.csv', index = False)
df = df.drop(columns = 'code')
df

# Chinese, Spanish, Russian, Japanese, French, and Korean
# Traditional Chinese, German, Italian, Portuguese, and Arabic
deepseek_client = OpenAI(api_key="***********************************", base_url="https://api.deepseek.com")
openai_client = OpenAI(api_key='********************************************************************************************************************************************************************')
claude3_client = anthropic.Anthropic(api_key="************************************************************************************************************")

def translate(text, model, first_half):
    if first_half:
        system_prompt = """
        The user will provide a text. Please translate the text in it into Chinese, Spanish, Russian, Japanese, French, and Korean and output the translated text in JSON format. 

        EXAMPLE INPUT: 
        name:  7 Cashmere Unicorn Hoodie in Off White, 
        color: Off White, 
        bullet1: 100% cashmere, 
        bullet2: Dry clean only, 
        bullet3: Button front, 
        bullet4: Front pockets, 
        bullet5: Styled with Acne Jeans Hex in Appearing, 
        bullet6: , 
        bullet7: , 
        bullet8: , 
        ingredients: , 
        subbrand: , 
        commondescription: , 
        rawdescription: Raw 7 takes the stuffy out of cashmere with these ultra plush creations. Luxurious enough for your next cocktail party yet Raw enough for your next rockstar party

        EXAMPLE JSON OUTPUT:
        {
            "Chinese": {
                "name": "7 羊绒独角兽连帽衫（米白色）",
                "color": "米白色",
                "bullet1": "100%羊绒",
                "bullet2": "仅限干洗",
                "bullet3": "前部纽扣设计",
                "bullet4": "前置口袋",
                "bullet5": "搭配Acne Jeans Hex牛仔裤（Appearing款）",
                "bullet6": "",
                "bullet7": "",
                "bullet8": "",
                "ingredients": "",
                "subbrand": "",
                "commondescription": "",
                "rawdescription": "Raw 7将羊绒的刻板印象一扫而空，以极其柔软的设计呈现。这既奢华到足以参加下一场鸡尾酒会，又酷炫到能让你在下一场摇滚派对中闪耀。"
            }, 
            "Spanish": {...}, 
            ...
        }
        """
    else:
        system_prompt = """
        The user will provide a text. Please translate the text in it into Traditional Chinese, German, Italian, Portuguese, and Arabic and output the translated text in JSON format. 

        EXAMPLE INPUT: 
        name:  7 Cashmere Unicorn Hoodie in Off White, 
        color: Off White, 
        bullet1: 100% cashmere, 
        bullet2: Dry clean only, 
        bullet3: Button front, 
        bullet4: Front pockets, 
        bullet5: Styled with Acne Jeans Hex in Appearing, 
        bullet6: , 
        bullet7: , 
        bullet8: , 
        ingredients: , 
        subbrand: , 
        commondescription: , 
        rawdescription: Raw 7 takes the stuffy out of cashmere with these ultra plush creations. Luxurious enough for your next cocktail party yet Raw enough for your next rockstar party

        EXAMPLE JSON OUTPUT:
        {
            "Traditional Chinese": {
                "name": "7 件羊絨獨角獸連帽衫（米白色）",
                "color": "米白色",
                "bullet1": "100% 羊絨",
                "bullet2": "僅限乾洗",
                "bullet3": "前扣",
                "bullet4": "前口袋",
                "bullet5": "搭配 Acne Jeans Hex 出現在風格中",
                "bullet6": "",
                "bullet7": "",
                "bullet8": "",
                "ingredients": "",
                "subbrand": "",
                "commondescription": "",
                "rawdescription": "Raw 7 以這些超柔軟的創作，將羊絨的沉悶感一掃而空。奢華到足以參加您的下一次雞尾酒派對，又足夠 Raw 以應對您的下一次搖滾明星派對。"
                }, 
            "German": {...}, 
            ...
        }
        """
    
    user_prompt = text
    messages = [{"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}]
    messages_claude = [{"role": "assistant", "content": system_prompt},
                {"role": "user", "content": user_prompt}]

    if model == 'deepseek':
        response = deepseek_client.chat.completions.create(
            model="deepseek-chat",
            messages=messages,
            response_format={
                'type': 'json_object'
            },
            temperature=1.3,
            stream=False
        )
    elif model == 'chatgpt':
        response = openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=messages,
            response_format={
                'type': 'json_object'
            },
            temperature=0
        )
    elif model == 'claude':
        response = claude3_client.messages.create(
            model="claude-3-5-sonnet-20241022",
            messages=messages_claude,
            temperature=0, 
            max_tokens=8192
        )
        return response.content[0].text

    return response.choices[0].message.content

model = 'claude'
responses = []
df_error = pd.DataFrame(columns = ['i', 'df_temp', 'text'])
for i in range(len(df)):
    start_time = datetime.now()
    df_temp = df.iloc[i, :]
    text = ', \n'.join([f'{ind}: {df_temp[ind]}' for ind in df_temp.index if df_temp[ind] != '' and pd.notnull(df_temp[ind])])
    if model == 'claude':
        text = text.replace('"', '\'')
    print(text)
    text = translate(text, model, first_half = True)
    try:
        response = json.loads(text)
        responses.append(response)
    except:
        df_error.loc[len(df_error), :] = [i, df_temp, text]
    print(f'{i}/60', (datetime.now() - start_time).seconds)
    # break
    # if i >= 1:
    #     break
with open('responses.pkl', 'wb') as f:
    pickle.dump(responses, f)

response2 = []
for i in range(len(df)):
    start_time = datetime.now()
    df_temp = df.iloc[i, :]
    text = ', \n'.join([f'{ind}: {df_temp[ind]}' for ind in df_temp.index if df_temp[ind] != '' and pd.notnull(df_temp[ind])])
    if model == 'claude':
        text = text.replace('"', '\'')
    print(text)
    text = translate(text, model, first_half = False)
    try:
        response = json.loads(text)
        response2.append(response)
    except:
        df_error.loc[len(df_error), :] = [i, df_temp, text]
    print(f'{i + 30}/60', (datetime.now() - start_time).seconds)
with open('response2.pkl', 'wb') as f:
    pickle.dump(response2, f)
    
file_path = f"translation_{model}.xlsx"
df_copy.to_excel(file_path, sheet_name = 'English', index=False)

for l in ['Spanish', 'Russian', 'Chinese', 'Japanese', 'French', 'Korean']:
    df_l = pd.DataFrame(columns = df_copy.columns)
    for i in range(len(responses)):
        df_l = pd.concat([df_l, pd.DataFrame([responses[i][l]])])
        df_l['code'] = code
    with pd.ExcelWriter(file_path, engine = 'openpyxl', mode = 'a', if_sheet_exists='replace') as writer:
        df_l.to_excel(writer, sheet_name = l, index = False)

for l in ['Traditional Chinese', 'German', 'Italian', 'Portuguese', 'Arabic']:
    df_l = pd.DataFrame(columns = df_copy.columns)
    for i in range(len(response2)):
        df_l = pd.concat([df_l, pd.DataFrame([response2[i][l]])])
        df_l['code'] = code
    with pd.ExcelWriter(file_path, engine = 'openpyxl', mode = 'a', if_sheet_exists='replace') as writer:
        df_l.to_excel(writer, sheet_name = l if l != 'Chinese' else 'Traditional Chinese', index = False)

file_path = f"translation_{model}.xlsx"
df_copy.to_excel(file_path, sheet_name = 'English', index=False)
for l in ['Spanish', 'Russian', 'Chinese', 'Japanese', 'French', 'Korean']:
    df_l = pd.DataFrame(columns = df_copy.columns)
    for i in range(len(responses_fix)):
        df_l = pd.concat([df_l, pd.DataFrame([responses_fix[i][l]])])
        df_l['code'] = code
    with pd.ExcelWriter(file_path, engine = 'openpyxl', mode = 'a', if_sheet_exists='replace') as writer:
        df_l.to_excel(writer, sheet_name = l, index = False)

for l in ['Traditional Chinese', 'German', 'Italian', 'Portuguese', 'Arabic']:
    df_l = pd.DataFrame(columns = df_copy.columns)
    for i in range(len(response2)):
        df_l = pd.concat([df_l, pd.DataFrame([response2[i][l]])])
        df_l['code'] = code
    with pd.ExcelWriter(file_path, engine = 'openpyxl', mode = 'a', if_sheet_exists='replace') as writer:
        df_l.to_excel(writer, sheet_name = l if l != 'Chinese' else 'Traditional Chinese', index = False)

system_prompt = """
        The user will provide a text. Please translate the text in it into Chinese, Spanish, Russian, Japanese, French, and Korean and output the translated text in JSON format. 

        EXAMPLE INPUT: 
        name:  7 Cashmere Unicorn Hoodie in Off White, 
        color: Off White, 
        bullet1: 100% cashmere, 
        bullet2: Dry clean only, 
        bullet3: Button front, 
        bullet4: Front pockets, 
        bullet5: Styled with Acne Jeans Hex in Appearing, 
        bullet6: , 
        bullet7: , 
        bullet8: , 
        ingredients: , 
        subbrand: , 
        commondescription: , 
        rawdescription: Raw 7 takes the stuffy out of cashmere with these ultra plush creations. Luxurious enough for your next cocktail party yet Raw enough for your next rockstar party

        EXAMPLE JSON OUTPUT:
        {
            "Chinese": {
                "name": "7 羊绒独角兽连帽衫（米白色）",
                "color": "米白色",
                "bullet1": "100%羊绒",
                "bullet2": "仅限干洗",
                "bullet3": "前部纽扣设计",
                "bullet4": "前置口袋",
                "bullet5": "搭配Acne Jeans Hex牛仔裤（Appearing款）",
                "bullet6": "",
                "bullet7": "",
                "bullet8": "",
                "ingredients": "",
                "subbrand": "",
                "commondescription": "",
                "rawdescription": "Raw 7将羊绒的刻板印象一扫而空，以极其柔软的设计呈现。这既奢华到足以参加下一场鸡尾酒会，又酷炫到能让你在下一场摇滚派对中闪耀。"
            }, 
            "Spanish": {...}, 
            ...
        }
        """
df_temp = df.iloc[17, :]
text = ', \n'.join([f'{ind}: {df_temp[ind]}' for ind in df_temp.index if df_temp[ind] != '' and pd.notnull(df_temp[ind])]).replace('"', '\'')
# print(text)
messages_claude = [{"role": "assistant", "content": system_prompt},
            {"role": "user", "content": text}]
response = claude3_client.messages.create(
            model="claude-3-5-sonnet-20241022",
            messages=messages_claude,
            temperature=0, 
            max_tokens=8192
        )
response_extra = response.content[0].text
pattern = r"(\{.*\})"
# Using re.search to find the match
match = re.search(pattern, response_extra, re.DOTALL).group(1)
print(match)

messages_claude = messages_claude + [
            {"role": "assistant", "content": response_extra},
            {"role": "user", "content": 'yes'}]
response_extra = claude3_client.messages.create(
            model="claude-3-5-sonnet-20241022",
            messages=messages_claude,
            temperature=0, 
            max_tokens=8192
        )
response_extra = response_extra.content[0].text
matches = json.loads(match)
match = re.search(pattern, response_extra, re.DOTALL).group(1)
matches.update(json.loads(match))
print(matches)

print(response_extra)

messages_claude = messages_claude + [
            {"role": "assistant", "content": response_extra2},
            {"role": "user", "content": 'yes'}]
response_extra2 = claude3_client.messages.create(
            model="claude-3-5-sonnet-20241022",
            messages=messages_claude,
            temperature=0, 
            max_tokens=8192
        )
response_extra2 = response_extra2.content[0].text
print(response_extra2)

# dict1 = json.loads(response_extra.replace("[Note: I can continue with the other languages (Russian, Japanese, French, Korean) but I stopped here due to length limitations. Would you like me to continue with the remaining languages?]", ''))
# dict2 = json.loads(response_extra2.replace("[Previous Chinese and Spanish translations...]", ''))
# dict1.update(dict2)
# dict1
responses_fix = responses[:16] + [dict1] + [matches] + responses[17:]
responses_fix

responses_fix[16]

# response2 = []
# for i in range(len(df)):
#     start_time = datetime.now()
#     df_temp = df.iloc[i, :]
#     text = ', \n'.join([f'{ind}: {df_temp[ind]}' for ind in df_temp.index if df_temp[ind] != ''])
#     # print(text)
#     text = translate(text, deepseek_client, first_half = False)
#     try:
#         response = json.loads(text)
#     except:
#         df_error.loc[len(df_error), :] = [i, df_temp, text]
#     response2.append(response)
#     print((datetime.now() - start_time).seconds)

# # Create an empty DataFrame
# # empty_df = pd.DataFrame()

# # Save the empty DataFrame to an Excel file
# file_path = f"translation_{model}.xlsx"
# df_copy.to_excel(file_path, sheet_name = 'English', index=False)

# for l in ['Spanish', 'Russian', 'Chinese', 'Japanese', 'French', 'Korean']:
#     df_l = pd.DataFrame(columns = df_copy.columns)
#     for i in range(len(responses)):
#         df_l = pd.concat([df_l, pd.DataFrame([responses[i][l]])])
#         df_l['code'] = code
#     with pd.ExcelWriter(file_path, engine = 'openpyxl', mode = 'a', if_sheet_exists='replace') as writer:
#         df_l.to_excel(writer, sheet_name = l, index = False)

# for l in ['Traditional Chinese', 'German', 'Italian', 'Portuguese', 'Arabic']:
#     df_l = pd.DataFrame(columns = df_copy.columns)
#     for i in range(len(response2)):
#         df_l = pd.concat([df_l, pd.DataFrame([response2[i][l]])])
#         df_l['code'] = code
#     with pd.ExcelWriter(file_path, engine = 'openpyxl', mode = 'a', if_sheet_exists='replace') as writer:
#         df_l.to_excel(writer, sheet_name = l if l != 'Chinese' else 'Traditional Chinese', index = False)

# pd.concat([pd.DataFrame([responses[i]['Chinese']]) for i in range(len(responses))]).columns
# pd.DataFrame([responses[0]['Chinese']]).loc[:, ['color', 'name']]