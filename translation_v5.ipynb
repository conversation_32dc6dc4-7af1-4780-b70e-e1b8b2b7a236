import sys
sys.path.insert(0, '/home/<USER>/bi_python_tool')
from openai import OpenAI
import pandas as pd
import json
from datetime import datetime
import anthropic
import pickle
import re
from tqdm import tqdm
# from ai_cost_calculator import *
from translator_cost import *
from translation_utils import *
import string


import Database_Connection_Class as db
conn = db.DBConnection(mars_cred={'bi_admin': 'work011DAY'},
                       venus_cred={'bi_admin': 'work011DAY'},
                       rs_cred={'bi_admin': 'work011DAY'},
                       rs_sslmode='verify-ca')

egn, _ = conn.new_session_PD()
sql_qry = f"""select id, english, `language`, `translation`, requestdate from `revolveclothing_com_-_db`.translation 
where translation='' and english != '' 
-- filter out image
and isimage = 0

-- filter out product pages
and not (url like '%product%' or section like '%product%' or productcode !='')

-- filter out programming commands
and english not like '%SUBSX_%'  
and english not like '%<div %'
and english not like '%<p %'
and english not like '%<span %'
and english not like '%<br%'  
and english not like '%alert%'
and english not like '%<script%'
and not (english REGEXP '^[,0-9-]+$')

and id not in (select id from bi.ym_translation_temp) and requestdate > '2023-01-01' and requestdate <= '2027-01-01'
order by requestdate"""


with egn.connect() as dbconn:
    df = pd.read_sql_query(sql_qry.replace('%', '%%'), dbconn)
print(df.shape)
print(df.language.value_counts())

language_list = ['zh', 'it', 'es']

df['english'] = df['english'].apply(lambda x: x.strip('\t\n ').strip())
df['first5_ascii'] = df['english'].apply(
    lambda x: all(c.isascii() for c in x[:5]) if isinstance(x, str) and x else False
)
df_filtered = df[df['first5_ascii']]
english_letter_pattern = re.compile(r'[A-Za-z]')
df_filtered['char_counts'] = df_filtered['english'].str.len()
df_filtered['english_counts'] = df_filtered['english'].str.count(r'[A-Za-z]')
df_filtered = df_filtered[df_filtered['english_counts'] > (df_filtered.char_counts / 2)]


# Function to count non-ASCII characters
def count_non_english(text):
    if pd.isna(text):
        return 0
    return len(re.findall(r'[^\x00-\x7F]', text))

# Apply the filter
df_filtered['non_english_count'] = df_filtered['english'].apply(count_non_english)
df_filtered = df_filtered[df_filtered['non_english_count'] <= 1]
df_filtered = df_filtered[df_filtered['english'].apply(lambda x : x[0] != '|')]
df_filtered = df_filtered[~df_filtered['english'].str.contains(r'\|\|')]
df_filtered = df_filtered[~df_filtered['english'].str.contains(r'concat\(')]
df_filtered = df_filtered[~df_filtered['english'].str.contains(r'chr\(')]
df_filtered = df_filtered[~df_filtered['english'].str.contains(r'navsrc')]
df_filtered = df_filtered[~df_filtered['english'].str.startswith('http')]
df_filtered = df_filtered[df_filtered['english'].apply(lambda x : len(x) > 10)]

#perform cleaning
# df_filtered['english'] = df_filtered['english'].apply(lambda x : x.replace('&amp;', '').replace('chr(10)', ' ').replace('chr(13)', ' ').replace('chr(9)', ' ').replace('chr(32)', ' ').replace('chr(34)', ' ').replace('chr(39)', ' ').replace('chr(40)', ' ').replace('chr(41)', ' '))

# df_filtered
df_gr = df_filtered[df_filtered['language'].isin(language_list)].groupby('english')['language'].unique().reset_index() 
# df_gr = df_filtered.groupby('english')['language'].unique().reset_index()
df_gr['language'] = df_gr['language'].apply(lambda x : list(set(x)))
df_gr['id_temp'] = range(len(df_gr))

# Convert list-of-lists column to list of tuples for deduplication
unique_language_lists = list({tuple(lst) for lst in df_gr['language']})

# Convert tuples back to lists (optional, if you want the result as list of lists)
unique_language_lists = [list(t) for t in unique_language_lists]

df_filtered['requestdate_month'] = df_filtered['requestdate'].apply(lambda x : f'{x.year}_{x.month:02d}')
df_filtered['english'].groupby(df_filtered['requestdate_month']).nunique()

df_summary = df_filtered[df_filtered['language'].isin(language_list)].groupby(['requestdate_month','english'])['language'].unique().reset_index() 
df_summary['language'] = df_summary['language'].apply(lambda x : ','.join(list(set(x))))
df_summary['count'] = 1
# df_summary = df_summary.groupby(['requestdate_month', 'language']).agg({'count': 'sum'}).reset_index()
df_summary.to_csv('df_summary.csv')

laguage_count = df_gr.language.value_counts().reset_index()
laguage_count

unique_language_lists = list(laguage_count[laguage_count['count'] >= 100]['language'].values)
unique_language_lists

df_gr.head()

import time

def produce_translate_chunk(unique_language_lists, df_gr, chunk_size = 2048):
    language_names = {
        "ar": "Arabic",
        "ko": "Korean",
        "pt": "Portuguese",
        "ru": "Russian",
        "de": "German",
        "fr": "French",
        "it": "Italian",
        "ch": "Swiss German",
        "zh": "Chinese (Simplified)",
        "ja": "Japanese",
        "es": "Spanish"
    }

    language_codes = {v: k for k, v in language_names.items()}
    df_output = pd.DataFrame()
    start_time = time.time()
    count_list = []
    count = 1
    grouped_lines_dict = {}

    for language_list in unique_language_lists:
        languages = ', '.join([language_names[x] for x in language_list])
        df_temp = df_gr[df_gr['language'].apply(lambda x: x == language_list)]

        grouped_lines = []
        current_chunk = ''
        # print(df_temp)
        for s, n in zip(df_temp['english'], df_temp['id_temp']):
            line = f'{n}. {s}' + '\n'
            # print(line)
            if len(current_chunk) + len(line) > chunk_size:
                if current_chunk:
                    grouped_lines.append(current_chunk)
                current_chunk = line 
                count_list.append(n)
                count = 1
            else:
                current_chunk += line
                count_list.append(n)
                count += 1
        if current_chunk:
            grouped_lines.append(current_chunk)
        grouped_lines_dict[languages] = grouped_lines
        print(languages, len(grouped_lines))
    return grouped_lines_dict

grouped_lines_dict = produce_translate_chunk(unique_language_lists, df_gr, chunk_size = 2048)

sample_language = list(grouped_lines_dict.keys())[2]
sample_line = grouped_lines_dict[sample_language][2]
print(sample_line, sample_language)


response = translate_gpt_general(sample_language, sample_line)
gpt_data = json.loads(response[0])
gpt_cost = calculate_gpt4_1mini_cost(response[1])
print(gpt_cost)


response_claude = translate_claude_general(sample_language, sample_line)
claude_data = json.loads(response_claude[0])
claude_cost = calculate_claude_3_7_sonnet_cost(response_claude[1])
print(claude_cost)

def convert_json_to_df(json_data, languages, grouped_line, df_filtered, translator = 'gpt'):
        rows = []
        for phrase, translations in json_data.items():
            for lan_codes, translation in translations.items():
                rows.append({'languages': languages, 
                            'grouped_line': grouped_line, 
                            'english': df_gr.loc[df_gr['id_temp'] == int(phrase), 'english'].iloc[0], 
                            'language': lan_codes, 
                            'translation': translation})

        df_output = pd.DataFrame(rows)
        df_output['language'] = df_output['language'].apply(lambda x : 'ch' if x == 'gsw' else ('zh' if 'zh' in x else x))
        df_final_output = df_filtered[['id', 'english', 'language']].merge(df_output, how = 'inner', on = ['english', 'language'])
        df_final_output['update_time'] = datetime.now()
        df_final_output['translator'] = translator
        return df_final_output, df_output
 

df_output_gpt, df_final_output_gpt = convert_json_to_df(gpt_data, sample_language, sample_line, df_filtered)
df_output_claude, df_final_output_claude = convert_json_to_df(claude_data, sample_language, sample_line, df_filtered, translator = 'claude')

df_output_claude.to_csv('df_output_claude_chinese_sample'+datetime.now().strftime('%Y%m%d%H%M%S')+'.csv', index = False, encoding='utf-8-sig')
df_output_gpt.to_csv('df_output_gpt_chinese_sample'+datetime.now().strftime('%Y%m%d%H%M%S')+'.csv', index = False, encoding='utf-8-sig')

start = time.time()
for languages, grouped_lines in grouped_lines_dict.items():
    print(languages)
    
    for grouped_line in tqdm(grouped_lines[:3]):
        cost = 0
        response = translate_gpt_general(languages, grouped_line)
        data = json.loads(response[0])
        cost += calculate_gpt4_1mini_cost(response[1])
        rows = []
        df_output, df_final_output = convert_json_to_df(data, languages, grouped_line, df_filtered, translator = 'gpt')
        print(len(df_output))
        # egn, _ = conn.new_session_PD()
        # with egn.connect() as dbconn:
        #     df_final_output[['id', 'translation', 'update_time']].to_sql('ym_translation_temp', egn, if_exists='append', 
        #     index=False)
        end_time = time.time()
        elapsed_time = end_time - start_time
        print(cost, elapsed_time)
        # with open('log.txt', 'w') as f:
        #     f.write(f'{start_time}, {end_time}, {elapsed_time}, ${cost}, {df_final_output.iloc[-1]}')
        # Convert timestamps to datetime
        start_dt = datetime.fromtimestamp(start_time)
        end_dt = datetime.fromtimestamp(end_time)

        # Define the format
        time_format = "%Y-%m-%d %H:%M:%S"
        break

# construct translation string

import time
language_names = {
    "ar": "Arabic",
    "ko": "Korean",
    "pt": "Portuguese",
    "ru": "Russian",
    "de": "German",
    "fr": "French",
    "it": "Italian",
    "ch": "Swiss German",
    "zh": "Chinese (Simplified)",
    "ja": "Japanese",
    "es": "Spanish"
}

language_codes = {v: k for k, v in language_names.items()}
df_output = pd.DataFrame()
start_time = time.time()
for l in unique_language_lists:
    print(l)
    languages = ', '.join([language_names[x] for x in l])
    df_temp = df_gr[df_gr['language'].apply(lambda x: x == l)]
    # for j in range(0, len(df_temp), 5):
    grouped_lines = []
    current_chunk = ''
    # print(df_temp)
    for s, n in zip(df_temp['english'], df_temp['id_temp']):
        line = f'{n}. {s}' + '\n'
        # print(line)
        if len(current_chunk) + len(line) > 2048:
            if current_chunk:
                grouped_lines.append(current_chunk)
            current_chunk = line 
        else:
            current_chunk += line
    if current_chunk:
        grouped_lines.append(current_chunk)
        # pharases = '\n'.join([f'- {x}' for x in df_temp['english'][j:j+5]])
        # with open('output.txt', 'a') as f:
        #     f.write(languages + '\n' + '\n'.join(grouped_lines) + '\n\n')
    # print(grouped_lines[0])
    for grouped_line in tqdm(grouped_lines[:3]):
        # print(grouped_line)
        # print('------------------------------')
        cost = 0
        response = translate_gpt(languages, grouped_line)
        # print(response)
        # break
        data = json.loads(response[0])
        cost += calculate_gpt4_1mini_cost(response[1])
        # break
        rows = []
        for phrase, translations in data.items():
            for lan_codes, translation in translations.items():
                rows.append({'languages': languages, 
                            'grouped_line': grouped_line, 
                            'english': df_gr.loc[df_gr['id_temp'] == int(phrase), 'english'].iloc[0], 
                            'language': lan_codes, 
                            'translation': translation})

        # df_output = pd.concat([df_output, pd.DataFrame(rows)], ignore_index=True)
        df_output = pd.DataFrame(rows)
        df_output['language'] = df_output['language'].apply(lambda x : 'ch' if x == 'gsw' else ('zh' if 'zh' in x else x))
        # df_output
        df_output.to_csv('df_output.csv', index = False)
        df_final_output = df_filtered[['id', 'english', 'language']].merge(df_output, how = 'inner', on = ['english', 'language'])
        df_final_output['update_time'] = datetime.now()
        print(len(df_output))
        # egn, _ = conn.new_session_PD()
        # with egn.connect() as dbconn:
        #     df_final_output[['id', 'translation', 'update_time']].to_sql('ym_translation_temp', egn, if_exists='append', 
        #     index=False)
        end_time = time.time()
        elapsed_time = end_time - start_time
        print(cost, elapsed_time)
        # with open('log.txt', 'w') as f:
        #     f.write(f'{start_time}, {end_time}, {elapsed_time}, ${cost}, {df_final_output.iloc[-1]}')
        # Convert timestamps to datetime
        start_dt = datetime.fromtimestamp(start_time)
        end_dt = datetime.fromtimestamp(end_time)

        # Define the format
        time_format = "%Y-%m-%d %H:%M:%S"
        break

print(grouped_line)

# json.loads(response[0])


# print(response[0][0])
# json.loads(response[0])
# row = df_output.iloc[3]
# row
# row['english']
# df_filtered[['id', 'english', 'language']]
# df_output
# df_filtered[['id', 'english', 'language']].merge(df_output, how = 'inner', on = ['english', 'language'])
# df_final_output[df_final_output['id'] == 498093789]
# with open('log.txt', 'w') as f:
#     log_line = (
#         f"Start Time: {start_dt.strftime(time_format)}, "
#         f"End Time: {end_dt.strftime(time_format)}, "
#         f"Elapsed Time: {elapsed_time:.2f} seconds, "
#         f"Cost: ${cost:.2f}, "
#         f"Final Output: {df_final_output['id'].iloc[-1]}"
#     )
#     f.write(log_line)

import requests
import os
import time
from datetime import datetime, timedelta

# Replace this with your actual API key if not using environment variable
OPENAI_ADMIN_KEY = "********************************************************************************************************************************************************************"

headers = {
    "Authorization": f"Bearer {OPENAI_ADMIN_KEY}",
    "Content-Type": "application/json"
}

yesterday = datetime.utcnow().date() - timedelta(days=1)
start_timestamp = int(time.mktime(yesterday.timetuple()))

url = "https://api.openai.com/v1/organization/costs"
params = {
    "start_time": start_timestamp,  # Unix timestamp
    "limit": 1
}

response = requests.get(url, headers=headers, params=params)

# Print the response (JSON formatted)
print(response.status_code)
print(response.json())


df_gr