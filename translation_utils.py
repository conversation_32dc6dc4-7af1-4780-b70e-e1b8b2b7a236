import anthropic
from openai import OpenAI

# ChatGPT
openai_client = OpenAI(
    api_key="********************************************************************************************************************************************************************"
)


def translate_gpt_jp(languages, pharases, gpt_model="gpt-4o-mini", max_tokens=8192):
    system_prompt = """
You are a professional localization expert translating fashion e-commerce phrases into Japanese.
Ignore any phrases that appear to be programming commands or code snippets, and do not include them in the output.
Preserve all HTML tags (<div>, <span>, etc.) exactly as they appear in the original text - do not translate the tags themselves, only the content between or around them.
Example:
For 'How to Send Back Your Return or Exchange <span>(US Orders)', the translation should be '返品・交換品のご返送方法 <span>(米国内ご注文分)' - preserving the <span> tag exactly as it appears in the original.
Do not translate brand names. Preserve the brand names exactly as it appears in the original.
Your output should be in JSON format, where each English phrase is a key, and the value is an object with translations in Japanese.
Use accurate, concise, and context-appropriate language that would be natural in an online fashion store (e.g., buttons, menus, product descriptions).
Only return valid, clean raw JSON object — no formatting, code fences, additional text or commentary.
Skip the phrases that may be programming commands.
    """
    structure = """
{
    "Phrase ID": {
        "ja": "Translation"
    },
    // more entries
}
"""
    
    pharases_prompt = f"""Here are the phrases to translate along with their phrase ID:
{pharases}"""
    user_prompt = f"""
Translate the following English phrases into Japanese.

These are UI or product-related terms used on a fashion e-commerce website. 
Please return the result in valid JSON, with this structure:
{structure}
{pharases_prompt}"""
    # print(user_prompt)
    messages = [
        {"role": "assistant", "content": system_prompt},
        {"role": "user", "content": user_prompt},
    ]
    response = openai_client.chat.completions.create(
        model=gpt_model,
        messages=messages,
        temperature=0,
        max_tokens=max_tokens,
        response_format={
            'type': 'json_object'
        },
    )
    return [response.choices[0].message.content, response.usage]


def translate_gpt_general(languages, pharases, gpt_model="gpt-4o-mini", max_tokens=8192):
    system_prompt = """
You are a professional localization expert translating fashion e-commerce phrases into multiple languages.
Ignore any phrases that appear to be programming commands or code snippets, and do not include them in the output.
Preserve all HTML tags (<div>, <span>, etc.) exactly as they appear in the original text - do not translate the tags themselves, only the content between or around them.
Example:
For 'How to Send Back Your Return or Exchange <span>(US Orders)', the Spanish translation should be 'Cómo tramitar tu Devolución o Cambio <span>(Pedidos EE UU)' - preserving the <span> tag exactly as it appears in the original.
Your output should be in JSON format, where each English phrase is a key, and the value is an object with translations in the specified languages.
Use accurate, concise, and context-appropriate language that would be natural in an online fashion store (e.g., buttons, menus, product descriptions).
Only return valid, clean raw JSON object — no formatting, code fences, additional text or commentary.
Skip the phrases that may be programming commands.
    """
    structure = """
{
    "Phrase ID": {
        "language code": "Translation",
        "language code": "Translation",
        // more entries
    },
    // more entries
}
"""
    
    pharases_prompt = f"""Here are the phrases to translate along with their phrase ID:
{pharases}"""
    user_prompt = f"""
Translate the following English phrases into {languages}.

These are UI or product-related terms used on a fashion e-commerce website. 
Please return the result in valid JSON, with this structure:
{structure}
{pharases_prompt}"""
    # print(user_prompt)
    messages = [
        {"role": "assistant", "content": system_prompt},
        {"role": "user", "content": user_prompt},
    ]
    response = openai_client.chat.completions.create(
        model=gpt_model,
        messages=messages,
        temperature=0,
        max_tokens=max_tokens,
        response_format={
            'type': 'json_object'
        },
    )
    return [response.choices[0].message.content, response.usage]


# Claude
# deepseek_client = OpenAI(
#     api_key="***********************************", base_url="https://api.deepseek.com"
# )
claude3_client = anthropic.Anthropic(
    api_key="************************************************************************************************************"
)

def translate_claude_general(languages, pharases, claude_model="claude-3-7-sonnet-20250219", max_tokens=8192):
    system_prompt = """
You are a professional localization expert translating fashion e-commerce phrases into multiple languages.
Ignore any phrases that appear to be programming commands or code snippets, and do not include them in the output.
Preserve all HTML tags (<div>, <span>, etc.) exactly as they appear in the original text - do not translate the tags themselves, only the content between or around them.
Example:
For 'How to Send Back Your Return or Exchange <span>(US Orders)', the Spanish translation should be 'Cómo tramitar tu Devolución o Cambio <span>(Pedidos EE UU)' - preserving the <span> tag exactly as it appears in the original.
Your output should be in JSON format, where each English phrase is a key, and the value is an object with translations in the specified languages.
Use accurate, concise, and context-appropriate language that would be natural in an online fashion store (e.g., buttons, menus, product descriptions).
Only return valid, clean raw JSON object — no formatting, code fences, additional text or commentary.
Skip the phrases that may be programming commands.
    """
    structure = """
{
    "Phrase ID": {
        "language code": "Translation",
        "language code": "Translation",
        // more entries
    }
}
"""
    
    pharases_prompt = f"""Here are the phrases to translate along with their phrase ID:
{pharases}"""
    user_prompt = f"""
Translate the following English phrases into {languages}.

These are UI or product-related terms used on a fashion e-commerce website. 
Please return the result in valid JSON, with this structure:
{structure}
{pharases_prompt}"""
    # print(user_prompt)
    messages_claude = [
        {"role": "assistant", "content": system_prompt},
        {"role": "user", "content": user_prompt},
    ]
    response = claude3_client.messages.create(
        model=claude_model,
        messages=messages_claude,
        temperature=0,
        max_tokens=max_tokens,
    )
    return [response.content[0].text, response.usage]



deepseek_client = OpenAI(api_key="***********************************", base_url="https://api.deepseek.com")
openai_client = OpenAI(api_key='********************************************************************************************************************************************************************')
claude3_client = anthropic.Anthropic(api_key="************************************************************************************************************")

def translate_general(languages, pharases, model='deepseek', model_version='gpt-4o-mini', max_tokens=8192):
    system_prompt = """
You are a professional localization expert translating fashion e-commerce phrases into multiple languages.
Ignore any phrases that appear to be programming commands or code snippets, and do not include them in the output.
Preserve all HTML tags (<div>, <span>, etc.) exactly as they appear in the original text - do not translate the tags themselves, only the content between or around them.
Example:
For 'How to Send Back Your Return or Exchange <span>(US Orders)', the Spanish translation should be 'Cómo tramitar tu Devolución o Cambio <span>(Pedidos EE UU)' - preserving the <span> tag exactly as it appears in the original.
Your output should be in JSON format, where each English phrase is a key, and the value is an object with translations in the specified languages.
Use accurate, concise, and context-appropriate language that would be natural in an online fashion store (e.g., buttons, menus, product descriptions).
Only return valid, clean raw JSON object — no formatting, code fences, additional text or commentary.
Skip the phrases that may be programming commands.
    """
    structure = """
{
    "Phrase ID": {
        "language code": "Translation",
        "language code": "Translation",
        // more entries
    },
    // more entries
}
"""
    
    pharases_prompt = f"""Here are the phrases to translate along with their phrase ID:
{pharases}"""
    user_prompt = f"""
Translate the following English phrases into {languages}.

These are UI or product-related terms used on a fashion e-commerce website. 
Please return the result in valid JSON, with this structure:
{structure}
{pharases_prompt}"""
    # print(user_prompt)
    messages = [
        {"role": "assistant", "content": system_prompt},
        {"role": "user", "content": user_prompt},
    ]

    user_prompt = text
    messages = [{"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}]
    messages_claude = [{"role": "assistant", "content": system_prompt},
                {"role": "user", "content": user_prompt}]

    if model == 'deepseek':
        response = deepseek_client.chat.completions.create(
            model="deepseek-chat",
            messages=messages,
            response_format={
                'type': 'json_object'
            },
            temperature=1.3,
            stream=False
        )
    elif model == 'chatgpt':
        response = openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=messages,
            response_format={
                'type': 'json_object'
            },
            temperature=0
        )
    elif model == 'claude':
        response = claude3_client.messages.create(
            model="claude-3-5-sonnet-20241022",
            messages=messages_claude,
            temperature=0, 
            max_tokens=8192
        )
        return response.content[0].text

    return response.choices[0].message.content